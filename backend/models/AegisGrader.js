import mongoose from "mongoose";

const aegisGraderSchema = new mongoose.Schema({
    testDetails: {
        createdBy: { type: String, required: true },
        className: { type: String, required: true },
        subject: { type: String, required: true },
        date: { type: String, required: true },
    },
    answerSheets: [
        {
            id: { type: String, required: true },
            studentName: { type: String, required: true },
            rollNumber: { type: String, required: true },
            pdfUrl: { type: String, required: true },
            timestamp: { type: Number, required: true },
            className: { type: String, required: true },
            evaluationResult: { type: mongoose.Schema.Types.Mixed },
            status: {
                type: String,
                enum: ['pending', 'processing', 'completed', 'error'],
                default: 'pending'
            },
            processedAt: { type: Date }
        },
    ],
    // New field for overall processing statistics
    processingStats: {
        totalAnswerSheets: { type: Number, default: 0 },
        successfulEvaluations: { type: Number, default: 0 },
        failedEvaluations: { type: Number, default: 0 },
        completedAt: { type: Date },
        processingStartedAt: { type: Date },
        overallStatus: {
            type: String,
            enum: ['pending', 'processing', 'completed', 'partial_failure'],
            default: 'pending'
        }
    },
    // Credit tracking for refunds
    creditInfo: {
        totalCreditsCharged: { type: Number, default: 0 },
        creditsRefunded: { type: Number, default: 0 },
        originalTransactionId: { type: String }, // Reference to the original credit deduction transaction
        refundTransactionIds: [{ type: String }] // Array of refund transaction IDs
    },
    questionPaper: {
        type: {
            type: String,
            enum: ['rubric', 'questionPaper'],
            required: false,
        },
        pdfUrl: { type: String, required: false },
        timestamp: { type: Number, required: false },
    },
    rubric: {
        type: {
            type: String,
            enum: ['rubric', 'questionPaper'],
            required: false,
        },
        pdfUrl: { type: String, required: false },
        timestamp: { type: Number, required: false },
    }
}, { collection: "AegisGrader", timestamps: true });

// Indexes for better query performance
aegisGraderSchema.index({ timestamp: -1 });
aegisGraderSchema.index({ "testDetails.createdBy": 1, createdAt: -1 });
aegisGraderSchema.index({ "processingStats.overallStatus": 1 });
aegisGraderSchema.index({ "creditInfo.originalTransactionId": 1 });

// Instance methods
aegisGraderSchema.methods.updateProcessingStats = function() {
    const totalSheets = this.answerSheets.length;
    const successful = this.answerSheets.filter(sheet => sheet.status === 'completed').length;
    const failed = this.answerSheets.filter(sheet => sheet.status === 'error').length;
    const processing = this.answerSheets.filter(sheet => sheet.status === 'processing').length;

    this.processingStats.totalAnswerSheets = totalSheets;
    this.processingStats.successfulEvaluations = successful;
    this.processingStats.failedEvaluations = failed;

    // Determine overall status
    if (processing > 0) {
        this.processingStats.overallStatus = 'processing';
    } else if (failed === 0) {
        this.processingStats.overallStatus = 'completed';
        this.processingStats.completedAt = new Date();
    } else if (successful > 0) {
        this.processingStats.overallStatus = 'partial_failure';
        this.processingStats.completedAt = new Date();
    } else {
        this.processingStats.overallStatus = 'completed'; // All failed, but processing is done
        this.processingStats.completedAt = new Date();
    }

    return this.save();
};

// Method to get failed answer sheets that need refunds
aegisGraderSchema.methods.getFailedSheetsForRefund = function() {
    return this.answerSheets.filter(sheet =>
        sheet.status === 'error' &&
        !this.creditInfo.refundTransactionIds.some(refundId =>
            refundId.includes(sheet.id)
        )
    );
};

// Method to calculate refund amount
aegisGraderSchema.methods.calculateRefundAmount = function() {
    const failedSheets = this.getFailedSheetsForRefund();
    return failedSheets.length; // 1 credit per failed sheet
};

// Static method to find submissions needing refunds
aegisGraderSchema.statics.findSubmissionsNeedingRefunds = function() {
    return this.find({
        'processingStats.overallStatus': { $in: ['completed', 'partial_failure'] },
        'processingStats.failedEvaluations': { $gt: 0 },
        $expr: {
            $gt: [
                '$processingStats.failedEvaluations',
                { $size: { $ifNull: ['$creditInfo.refundTransactionIds', []] } }
            ]
        }
    });
};

const AegisGrader = mongoose.model("AegisGrader", aegisGraderSchema);
export default AegisGrader;