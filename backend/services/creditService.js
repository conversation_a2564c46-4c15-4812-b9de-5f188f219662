import Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';
import CreditTransaction, { TRANSACTION_TYPES, TRANSACTION_STATUS, USAGE_TYPES } from '../models/CreditTransaction.js';
import { randomUUID } from 'crypto';

/**
 * Credit Management Service
 * Handles all credit-related operations including balance checks, deductions, additions, and transaction logging
 */
class CreditService {
    
    /**
     * Get user model based on user type
     */
    static getUserModel(userType) {
        return userType === 'Student' ? Student : Teacher;
    }

    /**
     * Determine user type by checking which collection the user exists in
     */
    static async determineUserType(userId) {
        try {
            // First check if user exists in Teacher collection
            const teacher = await Teacher.findById(userId).select('_id');
            if (teacher) {
                return 'Teacher';
            }

            // Then check if user exists in Student collection
            const student = await Student.findById(userId).select('_id');
            if (student) {
                return 'Student';
            }

            throw new Error('User not found in any collection');
        } catch (error) {
            console.error('Error determining user type:', error);
            throw error;
        }
    }

    /**
     * Get user's current credit balance
     */
    static async getCreditBalance(userId, userType) {
        try {
            const UserModel = this.getUserModel(userType);
            const user = await UserModel.findById(userId).select('credits');
            
            if (!user) {
                throw new Error('User not found');
            }
            
            return user.credits?.balance || 0;
        } catch (error) {
            console.error('Error getting credit balance:', error);
            throw error;
        }
    }
    
    /**
     * Check if user has sufficient credits
     */
    static async hasSufficientCredits(userId, userType, requiredCredits) {
        try {
            const balance = await this.getCreditBalance(userId, userType);
            return balance >= requiredCredits;
        } catch (error) {
            console.error('Error checking sufficient credits:', error);
            return false;
        }
    }
    
    /**
     * Deduct credits from user account
     */
    static async deductCredits(userId, userType, creditAmount, usageDetails) {
        const session = await Student.startSession();
        session.startTransaction();
        
        try {
            const UserModel = this.getUserModel(userType);
            const user = await UserModel.findById(userId).session(session);
            
            if (!user) {
                throw new Error('User not found');
            }
            
            const currentBalance = user.credits?.balance || 0;
            
            if (currentBalance < creditAmount) {
                throw new Error('Insufficient credits');
            }
            
            const newBalance = currentBalance - creditAmount;
            const totalSpent = (user.credits?.totalSpent || 0) + creditAmount;
            
            // Update user's credit balance
            await UserModel.findByIdAndUpdate(
                userId,
                {
                    $set: {
                        'credits.balance': newBalance,
                        'credits.totalSpent': totalSpent,
                        'credits.lastUpdated': new Date()
                    }
                },
                { session }
            );
            
            // Create transaction record
            const transaction = new CreditTransaction({
                userId,
                userType,
                transactionId: `usage_${randomUUID()}`,
                type: TRANSACTION_TYPES.USAGE,
                status: TRANSACTION_STATUS.COMPLETED,
                creditAmount,
                balanceBefore: currentBalance,
                balanceAfter: newBalance,
                usage: {
                    feature: usageDetails.feature || USAGE_TYPES.AEGIS_GRADER,
                    description: usageDetails.description,
                    relatedId: usageDetails.relatedId,
                    metadata: usageDetails.metadata
                },
                description: `Credit deduction for ${usageDetails.feature || 'service usage'}`,
                completedAt: new Date()
            });
            
            await transaction.save({ session });
            await session.commitTransaction();
            
            return {
                success: true,
                newBalance,
                transaction: transaction.toObject()
            };
            
        } catch (error) {
            await session.abortTransaction();
            console.error('Error deducting credits:', error);
            throw error;
        } finally {
            session.endSession();
        }
    }
    
    /**
     * Add credits to user account
     */
    static async addCredits(userId, userType, creditAmount, transactionDetails) {
        const session = await Student.startSession();
        session.startTransaction();
        
        try {
            const UserModel = this.getUserModel(userType);
            const user = await UserModel.findById(userId).session(session);
            
            if (!user) {
                throw new Error('User not found');
            }
            
            const currentBalance = user.credits?.balance || 0;
            const newBalance = currentBalance + creditAmount;
            const totalEarned = (user.credits?.totalEarned || 0) + creditAmount;
            
            // Update user's credit balance
            await UserModel.findByIdAndUpdate(
                userId,
                {
                    $set: {
                        'credits.balance': newBalance,
                        'credits.totalEarned': totalEarned,
                        'credits.lastUpdated': new Date()
                    }
                },
                { session }
            );
            
            // Create transaction record
            const transaction = new CreditTransaction({
                userId,
                userType,
                transactionId: transactionDetails.transactionId || `add_${randomUUID()}`,
                type: transactionDetails.type || TRANSACTION_TYPES.PURCHASE,
                status: TRANSACTION_STATUS.COMPLETED,
                creditAmount,
                balanceBefore: currentBalance,
                balanceAfter: newBalance,
                payment: transactionDetails.payment,
                description: transactionDetails.description || `Credit addition`,
                completedAt: new Date()
            });
            
            await transaction.save({ session });
            await session.commitTransaction();
            
            return {
                success: true,
                newBalance,
                transaction: transaction.toObject()
            };
            
        } catch (error) {
            await session.abortTransaction();
            console.error('Error adding credits:', error);
            throw error;
        } finally {
            session.endSession();
        }
    }

    /**
     * Refund credits to user account for failed operations
     */
    static async refundCredits(userId, userType, creditAmount, refundDetails) {
        const session = await Student.startSession();
        session.startTransaction();

        try {
            const UserModel = this.getUserModel(userType);
            const user = await UserModel.findById(userId).session(session);

            if (!user) {
                throw new Error('User not found');
            }

            const currentBalance = user.credits?.balance || 0;
            const newBalance = currentBalance + creditAmount;
            const totalEarned = (user.credits?.totalEarned || 0) + creditAmount;

            // Update user's credit balance
            await UserModel.findByIdAndUpdate(
                userId,
                {
                    $set: {
                        'credits.balance': newBalance,
                        'credits.totalEarned': totalEarned,
                        'credits.lastUpdated': new Date()
                    }
                },
                { session }
            );

            // Create refund transaction record
            const transaction = new CreditTransaction({
                userId,
                userType,
                transactionId: refundDetails.transactionId || `refund_${randomUUID()}`,
                type: TRANSACTION_TYPES.REFUND,
                status: TRANSACTION_STATUS.COMPLETED,
                creditAmount,
                balanceBefore: currentBalance,
                balanceAfter: newBalance,
                usage: {
                    feature: refundDetails.feature || USAGE_TYPES.AEGIS_GRADER,
                    description: refundDetails.description,
                    relatedId: refundDetails.relatedId,
                    metadata: {
                        ...refundDetails.metadata,
                        originalTransactionId: refundDetails.originalTransactionId,
                        refundReason: refundDetails.refundReason || 'Processing failure'
                    }
                },
                description: refundDetails.description || `Credit refund for failed processing`,
                completedAt: new Date()
            });

            await transaction.save({ session });
            await session.commitTransaction();

            return {
                success: true,
                newBalance,
                refundAmount: creditAmount,
                transaction: transaction.toObject()
            };

        } catch (error) {
            await session.abortTransaction();
            console.error('Error refunding credits:', error);
            throw error;
        } finally {
            session.endSession();
        }
    }

    /**
     * Process refunds for failed AegisGrader evaluations
     */
    static async processAegisGraderRefunds(aegisGraderSubmission) {
        try {
            const failedSheets = aegisGraderSubmission.getFailedSheetsForRefund();

            if (failedSheets.length === 0) {
                return {
                    success: true,
                    message: 'No failed sheets requiring refunds',
                    refundAmount: 0
                };
            }

            const refundAmount = failedSheets.length; // 1 credit per failed sheet
            const userId = aegisGraderSubmission.testDetails.createdBy;

            // Determine user type by checking which collection the user exists in
            const userType = await this.determineUserType(userId);

            const refundResult = await this.refundCredits(userId, userType, refundAmount, {
                transactionId: `aegis_refund_${aegisGraderSubmission._id}_${Date.now()}`,
                feature: USAGE_TYPES.AEGIS_GRADER,
                description: `Refund for ${refundAmount} failed AegisGrader evaluations`,
                relatedId: aegisGraderSubmission._id.toString(),
                originalTransactionId: aegisGraderSubmission.creditInfo.originalTransactionId,
                refundReason: 'Grading processing failure',
                metadata: {
                    submissionId: aegisGraderSubmission._id,
                    failedSheetIds: failedSheets.map(sheet => sheet.id),
                    testDetails: aegisGraderSubmission.testDetails
                }
            });

            // Update the AegisGrader submission with refund information
            aegisGraderSubmission.creditInfo.creditsRefunded += refundAmount;
            aegisGraderSubmission.creditInfo.refundTransactionIds.push(refundResult.transaction.transactionId);
            await aegisGraderSubmission.save();

            return {
                success: true,
                refundAmount,
                newBalance: refundResult.newBalance,
                failedSheetsCount: failedSheets.length,
                transaction: refundResult.transaction
            };

        } catch (error) {
            console.error('Error processing AegisGrader refunds:', error);
            throw error;
        }
    }

    /**
     * Get user's transaction history with enhanced filtering
     */
    static async getTransactionHistory(userId, userType, options = {}) {
        try {
            const query = {
                userId,
                userType,
                status: TRANSACTION_STATUS.COMPLETED
            };

            if (options.type) query.type = options.type;
            if (options.types && Array.isArray(options.types)) {
                query.type = { $in: options.types };
            }
            if (options.feature) query['usage.feature'] = options.feature;
            if (options.status) query.status = options.status;

            // Date range filtering
            if (options.dateFrom || options.dateTo) {
                query.createdAt = {};
                if (options.dateFrom) query.createdAt.$gte = new Date(options.dateFrom);
                if (options.dateTo) query.createdAt.$lte = new Date(options.dateTo);
            }

            const totalCount = await CreditTransaction.countDocuments(query);

            const transactions = await CreditTransaction.find(query)
                .sort({ createdAt: -1 })
                .limit(options.limit || 50)
                .skip(options.skip || 0);

            return {
                transactions,
                totalCount,
                totalPages: Math.ceil(totalCount / (options.limit || 50))
            };
        } catch (error) {
            console.error('Error getting transaction history:', error);
            throw error;
        }
    }

    /**
     * Get usage analytics for a user
     */
    static async getUsageAnalytics(userId, userType, timeRange = 'month') {
        try {
            const now = new Date();
            let startDate;

            switch (timeRange) {
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                case 'quarter':
                    startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                    break;
                case 'year':
                    startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            }

            const usageTransactions = await CreditTransaction.find({
                userId,
                userType,
                type: TRANSACTION_TYPES.USAGE,
                status: TRANSACTION_STATUS.COMPLETED,
                createdAt: { $gte: startDate }
            }).sort({ createdAt: -1 });

            const totalCreditsUsed = usageTransactions.reduce((sum, t) => sum + t.creditAmount, 0);
            const totalEvaluations = usageTransactions.length;
            const daysDiff = Math.ceil((now - startDate) / (1000 * 60 * 60 * 24));
            const averageCreditsPerDay = totalCreditsUsed / daysDiff;

            // Usage by feature
            const usageByFeature = {};
            usageTransactions.forEach(transaction => {
                const feature = transaction.usage?.feature || 'Unknown';
                if (!usageByFeature[feature]) {
                    usageByFeature[feature] = { count: 0, credits: 0 };
                }
                usageByFeature[feature].count++;
                usageByFeature[feature].credits += transaction.creditAmount;
            });

            // Add percentages
            Object.keys(usageByFeature).forEach(feature => {
                usageByFeature[feature].percentage =
                    totalCreditsUsed > 0 ? (usageByFeature[feature].credits / totalCreditsUsed) * 100 : 0;
            });

            const mostUsedFeature = Object.keys(usageByFeature).reduce((a, b) =>
                usageByFeature[a].count > usageByFeature[b].count ? a : b,
                Object.keys(usageByFeature)[0] || 'None'
            );

            // Daily usage aggregation
            const dailyUsage = this.aggregateUsageByPeriod(usageTransactions, 'day');
            const weeklyUsage = this.aggregateUsageByPeriod(usageTransactions, 'week');
            const monthlyUsage = this.aggregateUsageByPeriod(usageTransactions, 'month');

            return {
                totalCreditsUsed,
                totalEvaluations,
                averageCreditsPerDay: Math.round(averageCreditsPerDay * 100) / 100,
                mostUsedFeature,
                usageByFeature,
                dailyUsage,
                weeklyUsage,
                monthlyUsage
            };
        } catch (error) {
            console.error('Error getting usage analytics:', error);
            throw error;
        }
    }

    /**
     * Get payment analytics for a user
     */
    static async getPaymentAnalytics(userId, userType, timeRange = 'year') {
        try {
            const now = new Date();
            let startDate;

            switch (timeRange) {
                case 'month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                case 'quarter':
                    startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                    break;
                case 'year':
                    startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            }

            const purchaseTransactions = await CreditTransaction.find({
                userId,
                userType,
                type: TRANSACTION_TYPES.PURCHASE,
                status: TRANSACTION_STATUS.COMPLETED,
                createdAt: { $gte: startDate }
            }).sort({ createdAt: -1 });

            const totalAmountSpent = purchaseTransactions.reduce((sum, t) => sum + (t.payment?.amount || 0), 0);
            const totalCreditsPurchased = purchaseTransactions.reduce((sum, t) => sum + t.creditAmount, 0);
            const averageOrderValue = purchaseTransactions.length > 0 ? totalAmountSpent / purchaseTransactions.length : 0;

            // Purchases by package
            const purchasesByPackage = {};
            purchaseTransactions.forEach(transaction => {
                const packageType = transaction.payment?.packageType || 'Unknown';
                if (!purchasesByPackage[packageType]) {
                    purchasesByPackage[packageType] = {
                        count: 0,
                        totalAmount: 0,
                        totalCredits: 0
                    };
                }
                purchasesByPackage[packageType].count++;
                purchasesByPackage[packageType].totalAmount += transaction.payment?.amount || 0;
                purchasesByPackage[packageType].totalCredits += transaction.creditAmount;
            });

            // Add percentages
            Object.keys(purchasesByPackage).forEach(packageType => {
                purchasesByPackage[packageType].percentage =
                    totalAmountSpent > 0 ? (purchasesByPackage[packageType].totalAmount / totalAmountSpent) * 100 : 0;
            });

            const mostPopularPackage = Object.keys(purchasesByPackage).reduce((a, b) =>
                purchasesByPackage[a].count > purchasesByPackage[b].count ? a : b,
                Object.keys(purchasesByPackage)[0] || 'None'
            );

            // Monthly purchases aggregation
            const monthlyPurchases = this.aggregatePurchasesByMonth(purchaseTransactions);

            return {
                totalAmountSpent,
                totalCreditsPurchased,
                averageOrderValue: Math.round(averageOrderValue),
                mostPopularPackage,
                purchasesByPackage,
                monthlyPurchases
            };
        } catch (error) {
            console.error('Error getting payment analytics:', error);
            throw error;
        }
    }

    /**
     * Helper method to aggregate usage by time period
     */
    static aggregateUsageByPeriod(transactions, period) {
        const aggregated = {};

        transactions.forEach(transaction => {
            let key;
            const date = new Date(transaction.createdAt);

            switch (period) {
                case 'day':
                    key = date.toISOString().split('T')[0];
                    break;
                case 'week':
                    const weekStart = new Date(date);
                    weekStart.setDate(date.getDate() - date.getDay());
                    key = weekStart.toISOString().split('T')[0];
                    break;
                case 'month':
                    key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                    break;
                default:
                    key = date.toISOString().split('T')[0];
            }

            if (!aggregated[key]) {
                aggregated[key] = { credits: 0, evaluations: 0 };
            }

            aggregated[key].credits += transaction.creditAmount;
            aggregated[key].evaluations++;
        });

        return Object.keys(aggregated)
            .sort()
            .map(key => ({
                [period]: key,
                date: key,
                week: key,
                month: key,
                ...aggregated[key]
            }));
    }

    /**
     * Helper method to aggregate purchases by month
     */
    static aggregatePurchasesByMonth(transactions) {
        const aggregated = {};

        transactions.forEach(transaction => {
            const date = new Date(transaction.createdAt);
            const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

            if (!aggregated[key]) {
                aggregated[key] = { amount: 0, credits: 0, transactions: 0 };
            }

            aggregated[key].amount += transaction.payment?.amount || 0;
            aggregated[key].credits += transaction.creditAmount;
            aggregated[key].transactions++;
        });

        return Object.keys(aggregated)
            .sort()
            .map(key => ({
                month: key,
                ...aggregated[key]
            }));
    }
    
    /**
     * Get user's credit statistics
     */
    static async getCreditStats(userId, userType) {
        try {
            const UserModel = this.getUserModel(userType);
            const user = await UserModel.findById(userId).select('credits billing');
            
            if (!user) {
                throw new Error('User not found');
            }
            
            const [totalSpentResult, totalPurchasedResult] = await Promise.all([
                CreditTransaction.getTotalSpent(userId, userType),
                CreditTransaction.getTotalPurchased(userId, userType)
            ]);
            
            return {
                currentBalance: user.credits?.balance || 0,
                totalEarned: user.credits?.totalEarned || 0,
                totalSpent: user.credits?.totalSpent || 0,
                totalPurchased: totalPurchasedResult[0]?.totalCredits || 0,
                totalAmountSpent: user.billing?.totalAmountSpent || 0,
                lastUpdated: user.credits?.lastUpdated
            };
        } catch (error) {
            console.error('Error getting credit stats:', error);
            throw error;
        }
    }
    
    /**
     * Grant initial free credits to new users
     */
    static async grantInitialCredits(userId, userType, creditAmount = 10) {
        try {
            const transaction = await this.addCredits(userId, userType, creditAmount, {
                transactionId: `initial_${randomUUID()}`,
                type: TRANSACTION_TYPES.INITIAL_GRANT,
                description: `Welcome bonus: ${creditAmount} free credits`
            });
            
            return transaction;
        } catch (error) {
            console.error('Error granting initial credits:', error);
            throw error;
        }
    }
    
    /**
     * Create pending transaction for payment
     */
    static async createPendingTransaction(userId, userType, creditAmount, paymentDetails) {
        try {
            const UserModel = this.getUserModel(userType);
            const user = await UserModel.findById(userId).select('credits');
            
            if (!user) {
                throw new Error('User not found');
            }
            
            const currentBalance = user.credits?.balance || 0;
            
            const transaction = new CreditTransaction({
                userId,
                userType,
                transactionId: `purchase_${randomUUID()}`,
                type: TRANSACTION_TYPES.PURCHASE,
                status: TRANSACTION_STATUS.PENDING,
                creditAmount,
                balanceBefore: currentBalance,
                balanceAfter: currentBalance + creditAmount,
                payment: paymentDetails,
                description: `Credit purchase: ${paymentDetails.packageName}`
            });
            
            await transaction.save();
            return transaction.toObject();
        } catch (error) {
            console.error('Error creating pending transaction:', error);
            throw error;
        }
    }
}

export default CreditService;
