/**
 * Example of how the Lambda function would call the processing results endpoint
 * This demonstrates the expected payload structure and API usage
 */

import axios from 'axios';

// Example processing results that Lambda would send
const exampleProcessingResults = {
    manifestKey: "submission_67890_manifest.json",
    testDetails: {
        createdBy: "60f1b2b3c4d5e6f7a8b9c0d1", // Teacher ID
        className: "Class 10 A",
        subject: "Mathematics",
        date: "2025-01-15"
    },
    answerSheets: [
        {
            id: "student1.pdf",
            studentName: "<PERSON>",
            evaluationResult: "Grade: A+\nScore: 98/100\nExcellent work on algebraic equations. Clear methodology and correct answers throughout.",
            status: "completed",
            processedAt: "2025-01-15T10:30:00Z"
        },
        {
            id: "student2.pdf", 
            studentName: "<PERSON>",
            evaluationResult: "Grade: B\nScore: 82/100\nGood understanding of concepts. Minor calculation errors in question 3.",
            status: "completed",
            processedAt: "2025-01-15T10:31:00Z"
        },
        {
            id: "student3.pdf",
            studentName: "<PERSON>",
            evaluationResult: "Error: processing answer sheet, please try again",
            status: "error",
            processedAt: "2025-01-15T10:32:00Z"
        },
        {
            id: "student4.pdf",
            studentName: "<PERSON>",
            evaluationResult: "Grade: A\nScore: 91/100\nStrong performance overall. Well-structured solutions.",
            status: "completed",
            processedAt: "2025-01-15T10:33:00Z"
        },
        {
            id: "student5.pdf",
            studentName: "Edward Norton",
            evaluationResult: "Error: PDF parsing failed - corrupted file",
            status: "error",
            processedAt: "2025-01-15T10:34:00Z"
        }
    ],
    processingStats: {
        totalAnswerSheets: 5,
        successfulEvaluations: 3,
        failedEvaluations: 2,
        completedAt: "2025-01-15T10:45:00Z"
    },
    questionPaper: {
        type: "questionPaper",
        pdfUrl: "submission_67890_question.pdf",
        timestamp: 1705312800000
    },
    rubric: {
        type: "rubric", 
        pdfUrl: "submission_67890_rubric.pdf",
        timestamp: 1705312800000
    }
};

/**
 * Simulate Lambda function calling the processing results endpoint
 */
async function simulateLambdaCallback(baseUrl = 'http://localhost:5000') {
    try {
        console.log('🚀 Simulating Lambda function callback...');
        console.log('📊 Processing Results Summary:');
        console.log(`   Total Answer Sheets: ${exampleProcessingResults.processingStats.totalAnswerSheets}`);
        console.log(`   Successful: ${exampleProcessingResults.processingStats.successfulEvaluations}`);
        console.log(`   Failed: ${exampleProcessingResults.processingStats.failedEvaluations}`);
        console.log(`   Subject: ${exampleProcessingResults.testDetails.subject}`);
        console.log(`   Class: ${exampleProcessingResults.testDetails.className}\n`);

        // Call the processing results endpoint
        const response = await axios.post(
            `${baseUrl}/api/aegisGrader/processing-results`,
            exampleProcessingResults,
            {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 30000 // 30 second timeout
            }
        );

        console.log('✅ Processing results submitted successfully!');
        console.log('📝 Response:', response.data);

        // Check if refunds were processed
        if (response.data.refundsProcessed) {
            console.log('💰 Automatic refunds were processed for failed evaluations');
        }

        return response.data;

    } catch (error) {
        console.error('❌ Failed to submit processing results:', error.message);
        
        if (error.response) {
            console.error('📄 Response data:', error.response.data);
            console.error('📊 Status code:', error.response.status);
        }
        
        throw error;
    }
}

/**
 * Example of error-only processing results (all failed)
 */
const errorOnlyResults = {
    manifestKey: "submission_error_test_manifest.json",
    testDetails: {
        createdBy: "60f1b2b3c4d5e6f7a8b9c0d1",
        className: "Class 10 B", 
        subject: "Physics",
        date: "2025-01-15"
    },
    answerSheets: [
        {
            id: "student1.pdf",
            studentName: "Test Student 1",
            evaluationResult: "Error: PDF file corrupted",
            status: "error",
            processedAt: "2025-01-15T11:00:00Z"
        },
        {
            id: "student2.pdf",
            studentName: "Test Student 2", 
            evaluationResult: "Error: Handwriting recognition failed",
            status: "error",
            processedAt: "2025-01-15T11:01:00Z"
        }
    ],
    processingStats: {
        totalAnswerSheets: 2,
        successfulEvaluations: 0,
        failedEvaluations: 2,
        completedAt: "2025-01-15T11:05:00Z"
    }
};

/**
 * Simulate processing results with all failures (maximum refund scenario)
 */
async function simulateAllFailuresCallback(baseUrl = 'http://localhost:5000') {
    try {
        console.log('\n🚀 Simulating all-failures scenario...');
        console.log('📊 All answer sheets failed processing - full refund expected');

        const response = await axios.post(
            `${baseUrl}/api/aegisGrader/processing-results`,
            errorOnlyResults,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ All-failures results submitted successfully!');
        console.log('📝 Response:', response.data);
        console.log('💰 Full refund should be processed for all 2 failed sheets');

        return response.data;

    } catch (error) {
        console.error('❌ Failed to submit all-failures results:', error.message);
        throw error;
    }
}

/**
 * Test both scenarios
 */
async function runLambdaSimulation() {
    console.log('🧪 Lambda Processing Results Simulation\n');
    
    try {
        // Test mixed results (some success, some failures)
        await simulateLambdaCallback();
        
        // Test all failures scenario
        await simulateAllFailuresCallback();
        
        console.log('\n🎉 Lambda simulation completed successfully!');
        console.log('\n📋 Summary:');
        console.log('   - Mixed results: 3 successful, 2 failed → 2 credits refunded');
        console.log('   - All failures: 0 successful, 2 failed → 2 credits refunded');
        console.log('   - Automatic refund processing works for both scenarios');
        
    } catch (error) {
        console.error('\n💥 Lambda simulation failed:', error.message);
    }
}

// Export for use in other modules
export {
    simulateLambdaCallback,
    simulateAllFailuresCallback,
    runLambdaSimulation,
    exampleProcessingResults,
    errorOnlyResults
};

// Run simulation if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runLambdaSimulation();
}
