import { getAllSubmissions, getPresignedUrl, handleProcessingResults, processRefunds } from "../controllers/aegisGraderController.js";
import { verifyJWT } from "../middleware/verifyJWT.js";
import { checkAegisGraderCredits } from "../middleware/creditCheck.js";

import express from "express";

const router = express.Router();

// Apply JWT verification to all routes
router.use(verifyJWT);

// Legacy route (commented out since submitForGrading is no longer used)
// router.post("/submit", checkAegisGraderCredits, submitForGrading);
router.get("/submissions/:userId", getAllSubmissions);

// Apply credit check to presigned URL generation (new SQS/Lambda workflow)
router.post("/getPresigned", checkAegisGraderCredits, getPresignedUrl);

// Handle processing results from Lambda function (no JWT required for Lambda callbacks)
router.post("/processing-results", handleProcessingResults);

// Process refunds for failed evaluations (requires authentication)
router.post("/refunds/:submissionId?", processRefunds);

export default router;
