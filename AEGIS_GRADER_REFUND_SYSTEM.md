# AegisGrader Refund System Documentation

## Overview

The AegisGrader system has been enhanced to support automatic refunds for failed grading attempts. When answer sheets fail to be processed by the AI grading system, users will automatically receive credit refunds for those failed attempts.

## New AegisGrader Model Structure

### Enhanced Fields

```javascript
{
  // Existing fields...
  testDetails: { ... },
  answerSheets: [
    {
      // Existing fields...
      id: String,
      studentName: String,
      rollNumber: String,
      pdfUrl: String,
      timestamp: Number,
      className: String,
      evaluationResult: Mixed,
      
      // New status tracking fields
      status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'error'],
        default: 'pending'
      },
      processedAt: Date,
      errorMessage: String // For storing error details when status is 'error'
    }
  ],
  
  // New processing statistics
  processingStats: {
    totalAnswerSheets: Number,
    successfulEvaluations: Number,
    failedEvaluations: Number,
    completedAt: Date,
    processingStartedAt: Date,
    overallStatus: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'partial_failure'],
      default: 'pending'
    }
  },
  
  // New credit tracking for refunds
  creditInfo: {
    totalCreditsCharged: Number,
    creditsRefunded: Number,
    originalTransactionId: String, // Reference to original credit deduction
    refundTransactionIds: [String] // Array of refund transaction IDs
  }
}
```

## Refund System Workflow

### 1. Initial Credit Deduction
- When users request presigned URLs for grading, credits are deducted upfront (1 credit per answer sheet)
- An AegisGrader record is created with `creditInfo.originalTransactionId` storing the transaction ID
- All answer sheets start with `status: 'pending'`

### 2. Processing Results
- Lambda function processes the grading and calls `/api/aegisGrader/processing-results` endpoint
- Answer sheet statuses are updated to 'completed' or 'error'
- Processing statistics are calculated and stored

### 3. Automatic Refund Processing
- When processing results are received, the system automatically processes refunds for failed sheets
- Credits are refunded for answer sheets with `status: 'error'`
- Refund transactions are created and linked to the original submission

## API Endpoints

### POST /api/aegisGrader/processing-results
Handles processing results from Lambda function.

**Request Body:**
```javascript
{
  "manifestKey": "string",
  "testDetails": { ... },
  "answerSheets": [
    {
      "id": "student1.pdf",
      "studentName": "John Doe",
      "evaluationResult": "Grade: A, Score: 95/100...",
      "status": "completed", // or "error"
      "processedAt": "2025-01-15T10:30:00Z"
    }
  ],
  "processingStats": {
    "totalAnswerSheets": 25,
    "successfulEvaluations": 23,
    "failedEvaluations": 2,
    "completedAt": "2025-01-15T10:45:00Z"
  }
}
```

### POST /api/aegisGrader/refunds/:submissionId?
Manually process refunds for submissions with failed evaluations.

**Parameters:**
- `submissionId` (optional): Specific submission ID to process refunds for
- If no `submissionId` provided, processes refunds for all user's submissions needing refunds

**Response:**
```javascript
{
  "message": "Refund processing completed",
  "processedSubmissions": 2,
  "totalRefunded": 5,
  "results": [
    {
      "submissionId": "...",
      "refundAmount": 3,
      "failedSheetsCount": 3
    }
  ]
}
```

### GET /api/aegisGrader/submissions/:userId
Enhanced to include processing status and refund information.

**Response includes:**
- `status`: Overall submission status
- `processingStats`: Detailed processing statistics
- `creditInfo`: Credit and refund information

## Credit Service Enhancements

### New Methods

#### `refundCredits(userId, userType, creditAmount, refundDetails)`
Refunds credits to user account for failed operations.

#### `processAegisGraderRefunds(aegisGraderSubmission)`
Processes refunds for failed AegisGrader evaluations automatically.

#### `determineUserType(userId)`
Determines if a user is a Student or Teacher by checking both collections.

## Usage Examples

### Processing Lambda Results
```javascript
// Lambda function calls this endpoint when processing is complete
const results = {
  manifestKey: "submission_123_manifest.json",
  testDetails: { ... },
  answerSheets: [
    {
      id: "student1.pdf",
      status: "completed",
      evaluationResult: "Grade: A+",
      processedAt: new Date()
    },
    {
      id: "student2.pdf", 
      status: "error",
      evaluationResult: "Error: processing answer sheet, please try again",
      processedAt: new Date()
    }
  ],
  processingStats: {
    totalAnswerSheets: 2,
    successfulEvaluations: 1,
    failedEvaluations: 1,
    completedAt: new Date()
  }
};

// POST to /api/aegisGrader/processing-results
// System automatically refunds 1 credit for the failed sheet
```

### Manual Refund Processing
```javascript
// Process refunds for a specific submission
POST /api/aegisGrader/refunds/submission_id_here

// Process refunds for all user's submissions needing refunds
POST /api/aegisGrader/refunds
```

## Benefits

1. **Automatic Refunds**: Users automatically get credits back for failed processing
2. **Transparent Tracking**: Clear visibility into processing status and refunds
3. **Fair Billing**: Users only pay for successful evaluations
4. **Audit Trail**: Complete transaction history for credits and refunds
5. **Backward Compatibility**: Works with existing submissions using legacy status logic

## Migration Notes

- Existing submissions without the new fields will continue to work
- Legacy status determination logic is maintained for backward compatibility
- New submissions automatically use the enhanced tracking system
- Manual refund processing can be used for historical submissions if needed
